import os
import pandas as pd
import numpy as np
from PIL import Image, ImageDraw
import torch
from torch.utils import data
from datasets import utils


class RealLunarDataset(data.Dataset):
    """RealLunar Dataset for semantic segmentation
    
    A real lunar surface dataset containing terrain segmentation data.
    Dataset contains PCAM and TCAM images with bounding box annotations
    that are converted to semantic segmentation masks.
    
    Classes:
        0: Background (lunar surface)
        1: Green objects (small rocks/features)
        2: Blue objects (large rocks/features)
    """
    
    def __init__(self, root, split='train', transform=None, camera_type='both', merge_rocks=False):
        """
        Args:
            root (str): Root directory of the dataset
            split (str): Dataset split ('train', 'val', 'test')
            transform: Data augmentation transforms
            camera_type (str): Camera type to use ('PCAM', 'TCAM', 'both')
            merge_rocks (bool): If True, merge green and blue objects into single 'Rocks' class (2 classes total)
                               If False, keep separate green and blue object classes (3 classes total)
        """
        self.root = os.path.expanduser(root)
        self.split = split
        self.transform = transform
        self.camera_type = camera_type
        self.merge_rocks = merge_rocks

        # Dataset path
        self.dataset_path = os.path.join(self.root, 'RealLunar')

        # Class information - depends on merge_rocks setting
        if merge_rocks:
            self.num_classes = 2  # Background + Rocks (merged)
            self.class_names = ['Background', 'Rocks']
            print("RealLunar Dataset Mode: 2-class (Background + Rocks) - Compatible with Jiayu/LunarSim evaluation")
        else:
            self.num_classes = 3  # Background + Green objects + Blue objects
            self.class_names = ['Background', 'Green_Objects', 'Blue_Objects']
            print("RealLunar Dataset Mode: 3-class (Background + Green_Objects + Blue_Objects) - Original mode")

        self.ignore_index = 255

        # Color mapping for visualization (RGB format)
        self.background_color = [0, 0, 0]      # Black for background
        if merge_rocks:
            self.rock_color = [255, 0, 0]      # Red for merged rocks
        else:
            self.green_color = [0, 255, 0]     # Green for small objects
            self.blue_color = [0, 0, 255]      # Blue for large objects
        
        # Get all available samples
        self.samples = self._get_samples()
        
        print(f"Color Detection: Background={self.background_color}, Green={self.green_color}, Blue={self.blue_color}")
        
        # Split dataset: 70% train, 20% val, 10% test
        self.train_samples, self.val_samples, self.test_samples = self._split_dataset()
        
        # Select samples based on split
        if split == 'train':
            self.current_samples = self.train_samples
        elif split == 'val':
            self.current_samples = self.val_samples
        elif split == 'test':
            self.current_samples = self.test_samples
        else:
            raise ValueError(f"Invalid split: {split}. Must be 'train', 'val', or 'test'")
            
        print(f"RealLunar Dataset - {split}: {len(self.current_samples)} samples")

    def _get_samples(self):
        """Get all available samples from the dataset directory"""
        samples = []
        
        # Load bounding box data
        pcam_csv = os.path.join(self.dataset_path, 'PCAM_bounding_boxes.csv')
        tcam_csv = os.path.join(self.dataset_path, 'TCAM_bounding_boxes.csv')
        
        pcam_boxes = pd.read_csv(pcam_csv) if os.path.exists(pcam_csv) else pd.DataFrame()
        tcam_boxes = pd.read_csv(tcam_csv) if os.path.exists(tcam_csv) else pd.DataFrame()
        
        # Process PCAM images
        if self.camera_type in ['PCAM', 'both'] and not pcam_boxes.empty:
            for frame_id in pcam_boxes['Frame'].unique():
                img_path = os.path.join(self.dataset_path, f'PCAM{frame_id}.png')
                if os.path.exists(img_path):
                    frame_boxes = pcam_boxes[pcam_boxes['Frame'] == frame_id]
                    samples.append({
                        'camera': 'PCAM',
                        'frame_id': frame_id,
                        'image': img_path,
                        'boxes': frame_boxes
                    })
        
        # Process TCAM images
        if self.camera_type in ['TCAM', 'both'] and not tcam_boxes.empty:
            for frame_id in tcam_boxes['Frame'].unique():
                img_path = os.path.join(self.dataset_path, f'TCAM{frame_id}.png')
                if os.path.exists(img_path):
                    frame_boxes = tcam_boxes[tcam_boxes['Frame'] == frame_id]
                    samples.append({
                        'camera': 'TCAM',
                        'frame_id': frame_id,
                        'image': img_path,
                        'boxes': frame_boxes
                    })
        
        # Sort by camera type and frame id
        samples.sort(key=lambda x: (x['camera'], x['frame_id']))
        return samples
    
    def _split_dataset(self):
        """Split dataset into train/val/test with 7:2:1 ratio"""
        total_samples = len(self.samples)
        
        # Calculate split indices
        train_end = int(0.7 * total_samples)
        val_end = int(0.9 * total_samples)
        
        train_samples = self.samples[:train_end]
        val_samples = self.samples[train_end:val_end]
        test_samples = self.samples[val_end:]
        
        print(f"Dataset split - Train: {len(train_samples)}, Val: {len(val_samples)}, Test: {len(test_samples)}")

        return train_samples, val_samples, test_samples
    
    def _create_mask_from_boxes(self, image_size, boxes_df):
        """Create semantic segmentation mask from bounding boxes
        
        Args:
            image_size: (width, height) of the image
            boxes_df: DataFrame containing bounding box annotations
            
        Returns:
            numpy array with class indices
        """
        width, height = image_size
        mask = np.zeros((height, width), dtype=np.uint8)
        
        # Create PIL Image for drawing
        mask_pil = Image.fromarray(mask)
        draw = ImageDraw.Draw(mask_pil)
        
        for _, box in boxes_df.iterrows():
            x1 = int(box['TopLeftCornerX'])
            y1 = int(box['TopLeftCornerY'])
            x2 = int(x1 + box['Length'])
            y2 = int(y1 + box['Height'])

            # Determine class based on color and merge_rocks setting
            if box['Color'].lower() in ['green', 'blue']:
                if self.merge_rocks:
                    class_id = 1  # All rocks merged into class 1
                else:
                    if box['Color'].lower() == 'green':
                        class_id = 1  # Green objects
                    else:  # blue
                        class_id = 2  # Blue objects
            else:
                continue  # Skip unknown colors

            # Draw filled rectangle
            draw.rectangle([x1, y1, x2, y2], fill=class_id)
        
        return np.array(mask_pil)
    
    def __getitem__(self, index):
        """Get a sample from the dataset"""
        sample = self.current_samples[index]
        
        # Load image and convert to RGB
        image = Image.open(sample['image']).convert('RGB')
        
        # Create mask from bounding boxes
        mask_array = self._create_mask_from_boxes(image.size, sample['boxes'])
        mask = Image.fromarray(mask_array)
        
        # Apply transforms
        if self.transform is not None:
            image, mask = self.transform(image, mask)
        
        return image, mask
    
    def __len__(self):
        return len(self.current_samples)
    
    def get_class_names(self):
        """Get class names"""
        return self.class_names
    
    def get_class_colors(self):
        """Get class colors for visualization (RGB format)"""
        if self.merge_rocks:
            return [
                self.background_color,  # Background
                self.rock_color,        # Merged rocks
            ]
        else:
            return [
                self.background_color,  # Background
                self.green_color,       # Green objects
                self.blue_color,        # Blue objects
            ]

    def decode_target(self, target):
        """Convert class indices to RGB colors for visualization

        Args:
            target: numpy array with class indices

        Returns:
            numpy array with RGB colors for visualization
        """
        # Define color mapping based on mode
        if self.merge_rocks:
            color_map = np.array([
                self.background_color,  # Background
                self.rock_color,        # Merged rocks
            ], dtype=np.uint8)
        else:
            color_map = np.array([
                self.background_color,  # Background
                self.green_color,       # Green objects
                self.blue_color,        # Blue objects
            ], dtype=np.uint8)

        # Handle invalid class indices
        target = np.clip(target, 0, len(color_map) - 1)

        # Convert to RGB
        rgb_target = color_map[target]

        return rgb_target


if __name__ == '__main__':
    # Test the dataset
    import matplotlib.pyplot as plt

    # Test dataset loading
    dataset_root = '../datasets/data'

    print("Testing RealLunar Dataset in both modes...")

    for merge_rocks in [False, True]:
        mode_name = "2-class (merged rocks)" if merge_rocks else "3-class (original)"
        print(f"\n=== Testing {mode_name} mode ===")

        for split in ['train', 'val', 'test']:
            try:
                dataset = RealLunarDataset(root=dataset_root, split=split, merge_rocks=merge_rocks)
                print(f"\n{split.upper()} Dataset ({mode_name}):")
                print(f"  Samples: {len(dataset)}")
                print(f"  Classes: {dataset.num_classes}")
                print(f"  Class names: {dataset.get_class_names()}")

                if len(dataset) > 0:
                    # Test loading first sample
                    image, mask = dataset[0]
                    print(f"  Sample 0 - Image shape: {np.array(image).shape}, Mask shape: {np.array(mask).shape}")
                    print(f"  Mask unique values: {np.unique(np.array(mask))}")

            except Exception as e:
                print(f"Error loading {split} dataset: {e}")
