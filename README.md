# Segmentation Model Baseline

A comprehensive and extensible benchmark for semantic segmentation models, providing standardized training, evaluation, and comparison across multiple architectures and datasets.

## 🚀 Features

- **29 State-of-the-Art Models**: Support for CNN-based and Transformer-based architectures
- **Multiple Datasets**: Built-in support for various segmentation datasets with easy extension
- **Standardized Evaluation**: Consistent metrics and evaluation protocols for fair comparison
- **Performance Benchmarking**: Built-in tools for measuring FPS, FLOPs, and memory usage
- **Flexible Training**: Multiple loss functions, optimizers, and training strategies
- **Easy Extension**: Simple interfaces for adding new models and datasets
- **Comprehensive Visualization**: Training monitoring and result visualization tools

## 📋 Quick Start

### Installation

```bash
git clone https://github.com/evannli1/Segmentation-Model-Baseline.git
cd Segmentation-Model-Baseline
pip install -r requirements.txt
```



### Basic Usage

```bash
# Train a model
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --num_classes 5 --batch_size 4 --crop_size 512

# Evaluate a model
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --num_classes 5 --ckpt path/to/model.pth --test_only

# Performance benchmarking
python main.py --model segformer_b2 --dataset AI4Mars --ckpt path/to/model.pth --test_only --eval_performance
```

## 🏗️ Supported Models

### CNN-Based Models
| Model | Backbone Options | Description |
|-------|------------------|-------------|
| **DeepLabV3+** | ResNet50/101, MobileNetV2, Xception, HRNetV2 | Enhanced decoder with skip connections |
| **U-Net** | ResNet18/34/50/101/152, VGG16/19, DenseNet | Encoder-decoder with skip connections |

### Transformer-Based Models
| Model | Variants | Description |
|-------|----------|-------------|
| **SegFormer** | B0, B1, B2, B3, B4, B5 | Efficient transformer for segmentation |
| **Mask2Former** | Swin-Tiny/Small/Base/Large | Universal image segmentation |
| **UPerNet** | Swin-Tiny/Small/Base, ConvNeXt-Tiny | Unified perceptual parsing |

*Total: 25 model configurations*

## 📊 Supported Datasets

| Dataset | Classes | Domain | Description |
|---------|---------|---------|-------------|
| **AI4Mars** | 5 | Terrain | Mars rover terrain classification |
| **AI4Mars-SMI** | 5 | Terrain | Extended AI4Mars with semi-supervised learning |
| **LabelMars6** | 6 | Terrain | Alternative Mars terrain dataset |
| **Jiayu** | 2 | Terrain | Unity-generated rock segmentation dataset |
| **LunarSim** | 2 | Terrain | Lunar surface simulation rock segmentation dataset |
| **RealLunar** | 3 | Terrain | Real lunar surface images with object detection annotations |

### Jiayu Dataset Details

The Jiayu dataset is a Unity-generated rock segmentation dataset with the following characteristics:

| Property | Value |
|----------|-------|
| **Total Samples** | 197 |
| **Train/Val/Test Split** | 137/40/20 (70%/20%/10%) |
| **Image Size** | 1049×1545 pixels |
| **Format** | RGBA PNG images |
| **Classes** | Background (0), Rock (1) |
| **Rock Coverage** | 2.22%-8.81% (avg: 4.94%) |

**Usage Tips for Jiayu Dataset:**
- Use `--num_classes 2` for binary segmentation
- Recommended `--batch_size 2` due to large image size
- Consider `--loss_type focal_loss` for class imbalance
- Dataset automatically splits into train/val/test internally

### LunarSim Dataset Details

The LunarSim dataset is a lunar surface simulation dataset for rock segmentation with the following characteristics:

| Property | Value |
|----------|-------|
| **Total Samples** | 1641 |
| **Train/Val/Test Split** | 1148/328/165 (70%/20%/10%) |
| **Image Size** | 1280×720 pixels |
| **Format** | RGBA PNG images |
| **Classes** | Background (0), Rock (1) |
| **Sequences** | 4 sequences (01, 02, 03, 04) |
| **Data Source** | Lunar surface simulation |

**Usage Tips for LunarSim Dataset:**
- Use `--num_classes 2` for binary rock segmentation
- Recommended `--batch_size 4` for optimal performance
- Standard `--crop_size 512` works well
- Dataset automatically handles RGBA to RGB conversion
- Segmentation masks use G-channel for class information
- Suitable for lunar terrain analysis and rock detection

### RealLunar Dataset Details

The RealLunar dataset contains real lunar surface images with object detection annotations converted to semantic segmentation:

- **Total Samples**: Variable (based on available PCAM/TCAM images)
- **Image Sources**: PCAM (Panoramic Camera) and TCAM (Telephoto Camera)
- **Classes**: 3 classes
  - Background (0): Lunar surface terrain
  - Green Objects (1): Small rocks and surface features
  - Blue Objects (2): Large rocks and prominent features
- **Annotation Format**: Bounding boxes converted to semantic segmentation masks
- **Split Ratio**: 70% train / 20% validation / 10% test
- **Camera Types**: Supports PCAM, TCAM, or both camera types

**Usage Tips for RealLunar Dataset:**
- Use `--num_classes 3` for multi-class object segmentation
- Recommended `--batch_size 4` for optimal performance
- Standard `--crop_size 512` works well
- Dataset converts bounding box annotations to segmentation masks
- Supports filtering by camera type (PCAM, TCAM, or both)
- Suitable for real lunar terrain analysis and object detection

### Adding New Datasets
The framework supports easy integration of new datasets. See [Dataset Integration Guide](#dataset-integration) for details.

## 🛠️ Training & Evaluation

### Training a Model

```bash
# Basic training
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --num_classes 5 --lr 0.001 --batch_size 4 --crop_size 512 --total_epochs 200

# Training with Jiayu dataset (rock segmentation)
python main.py --model segformer_b2 --dataset Jiayu --num_classes 2 --batch_size 2 --crop_size 512 --loss_type focal_loss

# Training with LunarSim dataset (lunar rock segmentation)
python main.py --model segformer_b0 --dataset LunarSim --num_classes 2 --batch_size 4 --crop_size 512

# Training with RealLunar dataset (real lunar terrain segmentation)
python main.py --model segformer_b0 --dataset RealLunar --num_classes 3 --batch_size 4 --crop_size 512

# Training with different loss functions
python main.py --model segformer_b2 --dataset AI4Mars --loss_type focal_loss --batch_size 2 --crop_size 512

# Resume training from checkpoint
python main.py --model mask2former_swin_tiny --dataset AI4Mars --ckpt checkpoints/latest_model.pth --continue_training
```

### Model Evaluation

```bash
# Standard evaluation
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --ckpt path/to/model.pth --test_only --save_val_results

# Evaluate on Jiayu dataset
python main.py --model segformer_b2 --dataset Jiayu --num_classes 2 --ckpt path/to/model.pth --test_only

# Evaluate on LunarSim dataset
python main.py --model segformer_b0 --dataset LunarSim --num_classes 2 --ckpt path/to/model.pth --test_only --show_display

# Evaluate on RealLunar dataset
python main.py --model segformer_b0 --dataset RealLunar --num_classes 3 --ckpt path/to/model.pth --test_only --show_display

# Performance benchmarking
python main.py --model segformer_b2 --dataset AI4Mars --ckpt path/to/model.pth --test_only --eval_performance

# Evaluation on specific test splits
python main.py --model upernet_swin_tiny --dataset AI4Mars --ckpt path/to/model.pth --test_only --test_split testM1
```

### Supported Loss Functions

- **Cross-Entropy Loss**: Standard pixel-wise classification loss
- **Focal Loss**: Addresses class imbalance by down-weighting well-classified examples
- **Dice Loss**: Based on Dice coefficient, less sensitive to class imbalance

### Evaluation Metrics

- **IoU (Intersection over Union)**: Primary evaluation metric
- **Pixel Accuracy**: Overall pixel classification accuracy
- **F1 Score**: Harmonic mean of precision and recall
- **Class-specific metrics**: Per-class IoU and accuracy

## 🔧 Advanced Features

### Performance Benchmarking

The framework includes comprehensive performance evaluation tools:

```bash
# Measure FPS, FLOPs, and memory usage
python main.py --model segformer_b2 --dataset AI4Mars --ckpt path/to/model.pth --test_only --eval_performance
```

**Metrics Provided:**
- **FPS**: Frames per second on your hardware
- **FLOPs**: Floating point operations count
- **Parameters**: Model parameter count and memory usage
- **GPU Memory**: Peak GPU memory consumption

### Visualization Support

- **Training Monitoring**: Real-time loss and IoU tracking via Visdom
- **Result Visualization**: Segmentation mask overlays and comparisons
- **Performance Plots**: Training curves and metric evolution

```bash
# Start Visdom server
python -m visdom.server -port 8097

# Train with visualization
python main.py --model deeplabv3plus_resnet101 --dataset AI4Mars --enable_vis --vis_port 8097
```

### Adaptive Training Strategies

- **Automatic Optimizer Selection**: AdamW for Transformers, SGD for CNNs
- **Smart Learning Rate**: Automatic 0.1x adjustment for Transformer models
- **Gradient Clipping**: Prevents gradient explosion in Transformer models
- **Early Stopping**: Configurable patience and monitoring metrics

## 📈 Extension Guide

### Adding New Models

1. **Create Model Implementation**:
   ```python
   # In network/your_model.py
   class YourModel(nn.Module):
       def __init__(self, num_classes, **kwargs):
           super().__init__()
           # Your model implementation

       def forward(self, x):
           # Forward pass
           return output
   ```

2. **Register Model**:
   ```python
   # In network/modeling.py
   def your_model(num_classes=21, **kwargs):
       return YourModel(num_classes=num_classes, **kwargs)
   ```

3. **Update Available Models List**:
   The model will be automatically available via `--model your_model`

### Adding New Datasets

1. **Create Dataset Class**:
   ```python
   # In datasets/your_dataset.py
   class YourDataset(data.Dataset):
       def __init__(self, root, split='train', transform=None):
           # Dataset initialization

       def __getitem__(self, index):
           # Return image, label pair

       def __len__(self):
           # Return dataset size
   ```

2. **Register Dataset**:
   ```python
   # In main.py get_dataset() function
   if opts.dataset == 'YourDataset':
       train_dst = YourDataset(root=opts.data_root, split='train', transform=train_transform)
       val_dst = YourDataset(root=opts.data_root, split='val', transform=val_transform)
   ```

### Dataset Integration

The framework expects datasets to follow this structure:
```
datasets/data/YourDataset/
├── images/
│   ├── train/
│   ├── val/
│   └── test/
└── annotations/
    ├── train/
    ├── val/
    └── test/
```



## 🔗 References & Documentation

- **Model Commands**: See [MODEL_COMMANDS.md](MODEL_COMMANDS.md) for detailed training and evaluation commands
- **Changelog**: See [CHANGELOG.md](CHANGELOG.md) for version history and updates
- **Technical Details**: Advanced configuration options and implementation details
  

## 📄 Requirements

**System Requirements:**
- Python >= 3.8
- CUDA >= 11.8 (for GPU support)
- Windows/Linux compatible

See installation options in the [Quick Start](#-quick-start) section.

## 🤝 Contributing

We welcome contributions! Please follow these guidelines:

1. **Fork the repository** and create your feature branch
2. **Add new models** following the extension guide above
3. **Add new datasets** with proper documentation
4. **Include tests** for new functionality
5. **Update documentation** as needed
6. **Submit a pull request** with clear description

### Code Style
- Follow PEP 8 for Python code
- Add docstrings for new functions and classes
- Include type hints where appropriate

## 📜 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Original DeepLabV3+ implementation
- Hugging Face Transformers for model implementations
- PyTorch team for the deep learning framework
- Contributors to the various datasets

## 📞 Contact

For questions, issues, or contributions, please:
- Open an issue on GitHub
- Submit a pull request
- Check existing documentation and commands

---

**Happy Segmenting! 🎯**
